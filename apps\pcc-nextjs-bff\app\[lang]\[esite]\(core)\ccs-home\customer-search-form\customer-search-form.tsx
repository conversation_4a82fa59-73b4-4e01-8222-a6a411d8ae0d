'use client';

import { CatButton, CatInputField, CatCheckboxField, CatChip, CatInlineNotification, CatList, CatListItem } from 'components/blocks-components';
import { useForm, FormProvider } from 'react-hook-form';
import { useTranslations } from 'next-intl';
import { useCustomerSearch } from '../customer-search-provider';
import { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { getDealerInformation } from 'app/[lang]/[esite]/(checkout)/utils/get-dealer-information';
import { CustomerSearchResults } from './customer-search-results';

interface CustomerSearchFormState {
  firstName: string;
  lastName: string;
  logonId: string;
  company: string;
  email: string;
  phone: string;
  address1: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  companyName: string;
}

interface CountryInfo {
  callingCode: string;
  code: string;
  displayName: string;
  states: any[];
}

const CustomerSearchForm = () => {
  const t = useTranslations();
  const { performSearch, clearResults, isSearching, error, searchResults } = useCustomerSearch();
  const [isRefiningSearch, setIsRefiningSearch] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const autoCompletedInputs = useRef({ country: '' });
  const removeSpecialCharacterFeatureFlag = true;
  const [countryList, setCountryList] = useState<CountryInfo[]>([]);
  const [countrySearch, setCountrySearch] = useState('');
  const [filteredCountries, setFilteredCountries] = useState<CountryInfo[]>([]);
  const [showCountryDropdown, setShowCountryDropdown] = useState(false);
  const [hasSubmittedSearch, setHasSubmittedSearch] = useState(false);
  const [countryInputValue, setCountryInputValue] = useState('');

  const form = useForm<CustomerSearchFormState>({
    defaultValues: {
      firstName: '',
      lastName: '',
      logonId: '',
      company: '',
      email: '',
      phone: '',
      address1: '',
      city: '',
      state: '',
      zip: '',
      country: ''
    }
  });

  const { register, setValue, getValues, reset } = form;

  const normalizePhoneNumber = (phoneNumber: string) => {
    return removeSpecialCharacterFeatureFlag
      ? phoneNumber.replace(/[^\d]/g, '')
      : phoneNumber;
  };

  const onUpdateAutoCompletedInputs = useCallback(
    (fieldName: string, { code }: { code: string }) => {
      const currentValues = autoCompletedInputs.current;
      autoCompletedInputs.current = {
        ...currentValues,
        [fieldName]: code ?? currentValues[fieldName]
      };
    },
    [autoCompletedInputs]
  );

  const onSubmit = async (data: CustomerSearchFormState) => {
    const normalizedPhone = normalizePhoneNumber(data.phone);
    const formData = {
      ...data,
      phone: normalizedPhone,
      country: autoCompletedInputs.current.country || data.country,
      companyName: data.company
    };

    const hasValue = Object.values(formData).some(value => 
      value && value.toString().trim().length > 0
    );

    if (!hasValue) {
      console.error('All fields are blank');
      return;
    }

    try {
      await performSearch(formData, isRefiningSearch);
      setHasSubmittedSearch(true); // Set flag when search is submitted
    } catch (err) {
      console.error('Search failed:', err);
    }
  };

  const resetForm = () => {
    reset();
    clearResults();
    setIsRefiningSearch(false);
    setActiveFilters([]);
    autoCompletedInputs.current = { country: '' };
    setHasSubmittedSearch(false); // Reset flag when form is cleared
  };

  useEffect(() => {
    const fetchCountries = async () => {
      const { countryInformation } = await getDealerInformation();
      if (countryInformation?.countries) {
        setCountryList(countryInformation.countries);
      }
    };
    fetchCountries();
  }, []);

  const handleCountrySearch = useCallback((e) => {
    const search = e.detail.value.trim().toLowerCase();
    setCountrySearch(search);
    
    if (search.length > 0) {
      // Improved filtering to handle spaces and partial matches
      if (search.length >= 2) {
        // Split search into words for better matching
        const searchTerms = search.split(/\s+/).filter(term => term.length > 0);
        
        const filtered = countryList.filter(country => {
          const countryName = country.displayName.toLowerCase();
          
          // Match if country name contains all search terms in any order
          return searchTerms.every(term => countryName.includes(term));
        }).slice(0, 4);
        
        setFilteredCountries(filtered);
        setShowCountryDropdown(true);
      }
    } else {
      setFilteredCountries(countryList.slice(0, 4));
      setShowCountryDropdown(true);
    }
  }, [countryList]);

  const selectCountry = (country: CountryInfo) => {
    setValue('country', country.code);
    setCountrySearch(country.displayName);
    onUpdateAutoCompletedInputs('country', { code: country.code });
    setShowCountryDropdown(false);
  };

  // Add a click outside handler to close the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const dropdown = document.querySelector('.country-dropdown-container');
      if (dropdown && !dropdown.contains(target)) {
        setShowCountryDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const memoizedFilteredCountries = useMemo(() => {
    return filteredCountries;
  }, [filteredCountries]);

  const handleCountryInput = (e) => {
    const inputValue = e.target.value;
    setCountryInputValue(inputValue);
    setCountrySearch(inputValue);
    
    // Filter countries based on input
    if (inputValue && inputValue.length >= 2) {
      const searchTerms = inputValue.toLowerCase().split(/\s+/).filter(term => term.length > 0);
      
      const filtered = countryList.filter(country => {
        const countryName = country.displayName.toLowerCase();
        return searchTerms.every(term => countryName.includes(term));
      }).slice(0, 4);
      
      setFilteredCountries(filtered);
      setShowCountryDropdown(true);
    } else if (inputValue === '') {
      setFilteredCountries(countryList.slice(0, 4));
      setShowCountryDropdown(true);
    }
  };

  function highlightMatchedText(displayName: string, countrySearch: string): import("react").ReactNode {
    if (!countrySearch) {
      return displayName;
    }

    const searchTerms = countrySearch.toLowerCase().split(/\s+/).filter(term => term.length > 0);
    const regex = new RegExp(`(${searchTerms.join('|')})`, 'gi');

    const parts = displayName.split(regex);

    return parts.map((part, index) =>
      searchTerms.some(term => term === part.toLowerCase()) ? (
        <span key={index} className="font-weight-bold">
          {part}
        </span>
      ) : (
        part
      )
    );
  }

  return (
    <>
      <FormProvider {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="pb-3">
          {error && (
            <CatInlineNotification 
              variant="error"
              className="mb-4"
            >
              {t('SEARCH_ERROR')}: {error}
            </CatInlineNotification>
          )}
          <div className="row g-3">
            <div className="col-xs-12 col-md-6">
              <CatInputField
                type="text" 
                label={t('MUSREG_FNAME')}
                name="firstName"
                ref={register('firstName').ref}
                onBlChange={(e) => setValue('firstName', e.detail.value)}
                className="mb-3"
                size="md"
              />
              <CatInputField 
                type="text"
                label={t('MUSREG_LNAME')}
                name="lastName" 
                ref={register('lastName').ref}
                onBlChange={(e) => setValue('lastName', e.detail.value)}
                className="mb-3"
                size="md"
              />
              <CatInputField
                type="email"
                label={t('Email address')}
                name="email"
                ref={register('email').ref} 
                onBlChange={(e) => setValue('email', e.detail.value)}
                className="mb-3"
                size="md"
              />
            </div>
            <div className="col-xs-12 col-md-6">
              <CatInputField
                type="tel"
                label={t('BUY_APP_DETAIL_PHONE')} 
                name="phone"
                ref={register('phone').ref}
                onBlChange={(e) => setValue('phone', e.detail.value.replace(/[^\d]/g, ''))}
                className="mb-3"
                size="md"
              />
              <CatInputField
                type="text"
                label={t('LOGIN_ID_CSR')}
                name="logonId"
                ref={register('logonId').ref}
                onBlChange={(e) => setValue('logonId', e.detail.value)}
                className="mb-3"
                size="md"
              />
              <CatInputField
                type="text"
                label={t('CAT_EMAIL_COMPANYNAME')}
                name="companyName"
                ref={register('companyName').ref}
                onBlChange={(e) => setValue('companyName', e.detail.value)}
                className="mb-3"
                size="md"
              />
              <div className="position-relative country-dropdown-container">
                <div className="cat-input-field-wrapper">
                  <label className="cat-input-label">{t('CAT_EMAIL_COUNTRY')}</label>
                  <input
                    type="text"
                    className="form-control"
                    value={countryInputValue}
                    onChange={handleCountryInput}
                    onFocus={() => {
                      if (countryList.length > 0) {
                        setFilteredCountries(countryList.slice(0, 4));
                        setShowCountryDropdown(true);
                      }
                    }}
                    data-testid="country-search-input"
                  />
                </div>
                {showCountryDropdown && memoizedFilteredCountries.length > 0 && (
                  <div className="position-absolute bg-white shadow-sm w-100 z-10 max-h-60 overflow-auto border rounded">
                    <CatList spacing="condensed">
                      {memoizedFilteredCountries.map((country) => (
                        <CatListItem
                          key={country.code}
                          onClick={() => {
                            selectCountry(country);
                            setCountryInputValue(country.displayName);
                          }}
                          className="cursor-pointer hover:bg-gray-100 border-bottom border-gray-500"
                          data-testid={`country-option-${country.code}`}
                        >
                          {highlightMatchedText(country.displayName, countrySearch)}
                        </CatListItem>
                      ))}
                    </CatList>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="row">
            <div className="col-12 text-end mt-3">
              <CatButton
                type="button"
                variant="ghost"
                onClick={resetForm}
                disabled={isSearching}
                className="me-2"
              >
                {t('CLEAR_FILTERS')}
              </CatButton>
              <CatButton
                type="submit"
                variant="primary"
                disabled={isSearching}
                data-testid="search-submit-button"
                className="px-6"
                size="sm"
              >
                {isSearching ? t('SEARCHING') : t('SEARCH_CSR')}
              </CatButton>
            </div>
          </div>
        </form>
      </FormProvider>
      
      {/* Only render search results if search has been submitted and there are results */}
      {hasSubmittedSearch && <CustomerSearchResults />}
    </>
  );
};

export default CustomerSearchForm;
